version: '3'
services:
  minio:
    image: minio/minio
    platform: linux/amd64
    ports:
      - "9000:9000"
      - "9001:9001"
    expose:
      - "9000"
      - "9001"
    volumes:
      - minio_data_vol:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      MINIO_BROWSER_REDIRECT: "false"
      MINIO_SCANNER_SPEED: "fastest"
      MINIO_POLICY_PLUGIN_URL: "http://micro-auth/minio/authorization/"
    command: server --console-address ":9001" /data
    depends_on:
      - micro-auth
  postgis:
    image: postgis/postgis:15-3.3
    platform: linux/amd64
    environment:
      POSTGRES_PASSWORD: 'postgres'
    container_name: postgis
    hostname: postgis
    volumes:
      - "postgis_data_vol:/var/lib/postgresql/data"
    ports:
      - "54322:5432"
    stdin_open: true
    tty: true
  pgbouncer:
    image: edoburu/pgbouncer:latest
    platform: linux/amd64
    container_name: pgbouncer
    environment:
      DB_HOST: postgis
      DB_USER: "postgres"
      DB_PASSWORD: "postgres"
      DB_NAME: "postgres"
      DB_PORT: 5432
      LISTEN_ADDR: "*"
      LISTEN_PORT: 6432
      POOL_MODE: "transaction"
      AUTH_TYPE: "plain"
      MAX_CLIENT_CONN: 1000
      DEFAULT_POOL_SIZE: 5
      MIN_POOL_SIZE: 2
    ports:
      - "6432:6432"
    depends_on:
      - postgis
  rabbitmq:
    image: rabbitmq:3.8.2
    platform: linux/amd64
    container_name: rabbitmq
    volumes:
      - "rabbitmq_data_vol:/var/lib/rabbitmq"
  solr:
    image: solr:6.6
    platform: linux/amd64
    container_name: solr
    volumes:
      # hydroshare repository
      - ".:/hydroshare"
      - "solr_data_vol:/opt/solr/server/solr"
    ports:
      - "8983"
    command: ["solr-foreground"]
  hydroshare:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hydroshare
    hostname: hydroshare
    devices:
      - "/dev/fuse"
    privileged: true
    environment:
      POSTGIS_HOST: pgbouncer
      POSTGIS_PORT: 6432
      POSTGIS_PASSWORD: postgres
      POSTGIS_DB: postgres
      POSTGIS_USER: postgres
      PGPASSWORD: postgres
      RABBITMQ_PORT_5672_TCP_ADDR: rabbitmq
      SOLR_PORT_8983_TCP_ADDR: solr
      SOLR_HOST: solr
      TMP: /tmp
      POSTGIS_PORT_5432_TCP_ADDR: pgbouncer
      POSTGIS_DISABLE_SERVER_SIDE_CURSORS: "True"
      HS_PATH: ${PWD}
      PYTHONPATH: /hydroshare
      DJANGO_SETTINGS_MODULE: hydroshare.settings
      VUE_APP_BUCKET_URL_PUBLIC_PATH: "/static/static"
      MC_HOST_hydroshare: "***************************************"
    volumes:
      # hydroshare repository
      - ".:/hydroshare"
      # shared location for gunicorn.sock between containers
      - "temp_vol:/tmp"
      # temp directory shared with celery workers
      - "share_vol:/shared_tmp"
    ports:
      - "1338:2022"
      - "8000:8000"
    links:
      - postgis:postgis
      - solr:solr
      - rabbitmq:rabbitmq
      - minio:minio
    depends_on:
      - postgis
      - pgbouncer
      - solr
      - rabbitmq
      - minio
    stdin_open: true
    tty: true
    command: /bin/bash init-hydroshare
  defaultworker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: defaultworker
    platform: linux/amd64
    hostname: defaultworker
    environment:
      POSTGIS_HOST: pgbouncer
      POSTGIS_PORT: 6432
      POSTGIS_PASSWORD: postgres
      POSTGIS_DB: postgres
      POSTGIS_USER: postgres
      PGPASSWORD: postgres
      C_FORCE_ROOT: 1
      RABBITMQ_PORT_5672_TCP_ADDR: rabbitmq
      SOLR_PORT_8983_TCP_ADDR: solr
      SOLR_HOST: solr
      POSTGIS_PORT_5432_TCP_ADDR: pgbouncer
      POSTGIS_DISABLE_SERVER_SIDE_CURSORS: "True"
      HS_PATH: ${PWD}
      PYTHONPATH: /hydroshare
      DJANGO_SETTINGS_MODULE: hydroshare.settings
      COLUMNS: 80
      MC_HOST_hydroshare: "***************************************"
    ports:
      - "5555:5555"
    volumes:
      - ".:/hydroshare"
    links:
      - pgbouncer:pgbouncer
      - rabbitmq:rabbitmq
    depends_on:
      - hydroshare
      - pgbouncer
      - solr
      - rabbitmq
    stdin_open: true
    tty: true
    command: /bin/bash init-defaultworker
  companion:
  # https://github.com/transloadit/uppy/blob/main/packages/%40uppy/companion/KUBERNETES.md
    image:  docker.io/transloadit/companion:5.5.0
    platform: linux/amd64
    container_name: companion
    hostname: companion
    environment:
      COMPANION_PORT: '3020'
      COMPANION_CLIENT_ORIGINS: 'https://localhost'
      # when testing 3rd party services like google drive, you will access hydroshare at http://host.docker.internal:8000
      # you will need to add a line in your /etc/hosts file to map host.docker.internal to localhost
      COMPANION_UPLOAD_URLS: 'https://localhost/hsapi/tus/*'
      COMPANION_DATADIR: '/mnt/companion-data'
      COMPANION_DOMAIN: 'localhost'
      COMPANION_PATH: '/companion'
      COMPANION_ALLOW_LOCAL_URLS: 'true'
      COMPANION_PROTOCOL: 'https'
      COMPANION_REDIS_URL: 'redis://redis:6379'
      COMPANION_CHUNK_SIZE: '100000000'  # ~72MB
      # COMPANION_PREAUTH_SECRET: 'another secret'
      # For 3rd party services like google drive to work locally, you need to set the following:
      COMPANION_SECRET: 'SECRET'
      # Intentionally added to repo for testing purposes
      COMPANION_GOOGLE_KEY: '737951655407-p3d2b2bl2ln90g5plfj09e98bprk42da.apps.googleusercontent.com'
      COMPANION_GOOGLE_SECRET: '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'
      # https://uppy.io/docs/companion/#enablegooglepickerendpoint-companion_enable_google_picker_endpoint
      COMPANION_ENABLE_GOOGLE_PICKER_ENDPOINT: 'true'
    ports:
      - "3020:3020"
    volumes:
      - "companion_vol:/mnt/companion-data"
    depends_on:
      - redis
  redis:
    image: redis:latest
    platform: linux/amd64
    container_name: redis
    hostname: redis
    ports:
      - "6379:6379"
    volumes:
      - "redis_data_vol:/data"
  nginx:
    image: nginx:latest
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx-local-dev.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/config:/etc/ssl
    depends_on:
      - hydroshare
      - companion
  micro-auth:
    image: hydroshare/micro-auth:latest
    platform: linux/amd64
    environment:
      HS_DATABASE_URL: "*******************************************/postgres"
      MC_HOST_hydroshare: "***************************************"
      REDIS_HOST: redis
      REDIS_PORT: "6379"
    ports:
      - 8001:8001
    links:
      - postgis:postgis
    restart: on-failure
    depends_on:
      - postgis
volumes:
  postgis_data_vol:
  solr_data_vol:
  temp_vol:
  share_vol:
  rabbitmq_data_vol:
  minio_data_vol:
  redis_data_vol:
  companion_vol:
